{"type": "module", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src", "format": "prettier src --check", "validate": "vue-tsc -b", "test": "vitest", "test:run": "vitest run"}, "dependencies": {"@eslint/js": "9.17.0", "@typescript-eslint/parser": "8.18.2", "@vitejs/plugin-vue": "5.2.1", "@vue/test-utils": "^2.4.6", "eslint": "9.17.0", "eslint-plugin-vue": "9.32.0", "jsdom": "^26.1.0", "prettier": "3.4.2", "typescript": "5.7.2", "typescript-eslint": "8.18.2", "vite": "6.0.6", "vitest": "^3.2.4", "vue": "3.5.13", "vue-tsc": "2.2.0"}}